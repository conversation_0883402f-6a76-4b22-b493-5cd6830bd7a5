PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_archive (0.0.1):
    - FlutterMacOS
    - ZIPFoundation (= 0.9.19)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB/SQLCipher (2.7.11):
    - SQLCipher (~> 4.0)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - location (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqflite_sqlcipher (0.0.1):
    - FlutterMacOS
    - FMDB/SQLCipher (~> 2.7.5)
    - SQLCipher (= 4.5.7)
  - SQLCipher (4.5.7):
    - SQLCipher/standard (= 4.5.7)
  - SQLCipher/common (4.5.7)
  - SQLCipher/standard (4.5.7):
    - SQLCipher/common
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - ZIPFoundation (0.9.19)

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_archive (from `Flutter/ephemeral/.symlinks/plugins/flutter_archive/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin`)
  - location (from `Flutter/ephemeral/.symlinks/plugins/location/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - sqflite_sqlcipher (from `Flutter/ephemeral/.symlinks/plugins/sqflite_sqlcipher/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - FMDB
    - SQLCipher
    - ZIPFoundation

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_archive:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_archive/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin
  location:
    :path: Flutter/ephemeral/.symlinks/plugins/location/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  sqflite_sqlcipher:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_sqlcipher/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 0a976dfd033b59192912fa3c6c7b54aab5093802
  device_info_plus: 1b14eed9bf95428983aed283a8d51cce3d8c4215
  file_picker: e716a70a9fe5fd9e09ebc922d7541464289443af
  file_selector_macos: cc3858c981fe6889f364731200d6232dac1d812d
  flutter_archive: 52a43c0bc9d310d2fdfc5c54c7d7ccf4c624bc9a
  flutter_local_notifications: 4b427ffabf278fc6ea9484c97505e231166927a5
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 57486c1117fd8e0e6b947b2f54c3f42bf8e57a4e
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  location: 7cdb0665bd6577d382b0a343acdadbcb7f964775
  package_info_plus: 12f1c5c2cfe8727ca46cbd0b26677728972d9a5b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  sqflite_sqlcipher: 87b476a4b1dbaa86041d5be30112d33ba7b48d07
  SQLCipher: 5e6bfb47323635c8b657b1b27d25c5f1baf63bf5
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404
  ZIPFoundation: b8c29ea7ae353b309bc810586181fd073cb3312c

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
