// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:ipf_flutter_starter_pack/bases.dart';

// class NotificationService extends BaseNotificationService {
// 	NotificationService._(): super("app_icon");
// 	static final NotificationService _instance = NotificationService._();
// 	static NotificationService get instance => _instance;

// 	@override
// 	void selectNotification(BuildContext context, NotificationResponse response) {
// 		// TODO: implement selectNotification
// 	}
// }
