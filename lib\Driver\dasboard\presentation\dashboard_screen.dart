import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/dashboard_viewmodel.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/data/dashboard_repository.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/widgets/driver_status_toggle.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/widgets/ride_request_card.dart';
import 'package:sepesha_app/Driver/history/presentation/history_screen.dart';
import 'package:sepesha_app/Driver/model/ride_model.dart';
import 'package:sepesha_app/Driver/model/user_model.dart';
import 'package:sepesha_app/Driver/wallet/presentation/wallet_screen.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/components/app_button.dart';
import 'package:sepesha_app/provider/payment_provider.dart';
import 'package:sepesha_app/screens/payment_methods_screen.dart';
import 'package:sepesha_app/widgets/smart_driver_rating.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  GoogleMapController? _mapController;
  final LatLng _initialPosition = const LatLng(
    37.7749,
    -122.4194,
  ); // Default to San Francisco
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DashboardViewModel(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Driver Dashboard'),
          actions: [
            IconButton(
              icon: const Icon(Icons.history),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HistoryScreen(),
                  ),
                );
              },
            ),
          ],
          surfaceTintColor: AppColor.white,
          backgroundColor: AppColor.white,
        ),
        drawer: _buildDrawer(context),
        body: Consumer<DashboardViewModel>(
          builder: (context, viewModel, child) {
            // Update map when ride is accepted
            if (viewModel.currentRide != null && _mapController != null) {
              _updateMapForRide(viewModel.currentRide!);
            }

            return Stack(
              children: [
                // Map View
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _initialPosition,
                    zoom: 14,
                  ),
                  markers: _markers,
                  polylines: _polylines,
                  onMapCreated: (controller) {
                    _mapController = controller;
                    if (viewModel.currentRide != null) {
                      _updateMapForRide(viewModel.currentRide!);
                    }
                  },
                  myLocationEnabled: true,
                  myLocationButtonEnabled: true,
                ),

                // Dashboard Content Overlay
                if (viewModel.currentRide == null) ...[
                  // In your DashboardScreen build method:
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: Column(
                      children: [
                        DriverStatusToggle(
                          initialStatus: viewModel.isOnline,
                          onStatusChanged: (bool isOnline) async {
                            try {
                              viewModel.toggleOnlineStatus();
                              return true; // Return true if successful
                            } catch (e) {
                              return false; // Return false if failed
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],

                // Ride Request Cards (when available)
                if (viewModel.pendingRides.isNotEmpty &&
                    viewModel.currentRide == null)
                  Positioned(
                    top: 16,
                    left: 16,
                    right: 16,
                    child: Container(
                      constraints: BoxConstraints(
                        maxHeight:
                            MediaQuery.of(context).size.height *
                            0.45, // Limit height to 40% of screen
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ...viewModel.pendingRides.map(
                              (ride) => RideRequestCard(
                                ride: ride,
                                onAccept: () => viewModel.acceptRide(ride),
                                onReject: () => viewModel.rejectRide(ride),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // Current Ride Info (when active)
                if (viewModel.currentRide != null)
                  Positioned(
                    bottom: 16,
                    left: 16,
                    right: 16,
                    child: _buildCurrentRideCard(viewModel.currentRide!),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _updateMapForRide(Ride ride) async {
    // In a real app, you would get these coordinates from geocoding services
    final pickupLatLng = const LatLng(37.7749, -122.4194); // Example pickup
    final dropoffLatLng = const LatLng(37.3352, -122.0324); // Example dropoff

    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('pickup'),
          position: pickupLatLng,
          infoWindow: const InfoWindow(title: 'Pickup Location'),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
        Marker(
          markerId: const MarkerId('dropoff'),
          position: dropoffLatLng,
          infoWindow: const InfoWindow(title: 'Dropoff Location'),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      };

      // In a real app, you would calculate the route using a directions API
      _polylines = {
        Polyline(
          polylineId: const PolylineId('route'),
          points: [
            pickupLatLng,
            LatLng(pickupLatLng.latitude + 0.01, pickupLatLng.longitude - 0.01),
            LatLng(
              dropoffLatLng.latitude - 0.01,
              dropoffLatLng.longitude + 0.01,
            ),
            dropoffLatLng,
          ],
          color: AppColor.blue2,
          width: 5,
        ),
      };
    });

    // Zoom to fit both markers
    _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(
        LatLngBounds(northeast: pickupLatLng, southwest: dropoffLatLng),
        100, // padding
      ),
    );
  }

  Widget _buildCurrentRideCard(Ride ride) {
    return Card(
      color: AppColor.white.withOpacity(0.9),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  ride.passengerName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Tsh ${ride.fare.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColor.greenBullet,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Pickup:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(ride.pickupAddress),
            const SizedBox(height: 8),
            const Text(
              'Destination:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(ride.destinationAddress),
            const SizedBox(height: 16),
            ContinueButton(
              onPressed: () {},
              isLoading: false,
              text: 'Start Ride',
              backgroundColor: AppColor.primary,
              textColor: AppColor.white,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return FutureBuilder<User>(
      future: DashboardRepository().getUserData(),
      builder: (context, snapshot) {
        // Show loading while fetching user data
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Drawer(child: Center(child: CircularProgressIndicator()));
        }

        // Use fetched data or fallback
        final driver =
            snapshot.data ??
            User(
              id: 'fallback',
              name: 'Driver',
              email: '<EMAIL>',
              phone: '+************',
              vehicleNumber: 'N/A',
              vehicleType: 'Car',
              walletBalance: 0.0,
              rating: 0.0,
              totalRides: 0,
            );

        return Drawer(
          child: ListView(
            padding: EdgeInsets.zero,
            children: [
              UserAccountsDrawerHeader(
                accountName: Text(driver.name),
                accountEmail: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(driver.email),
                    const SizedBox(height: 4),
                    SmartDriverRating(
                      driverId: driver.id,
                      iconSize: 12.0,
                      fallbackRating: driver.rating,
                      fallbackReviews: driver.totalRides,
                    ),
                  ],
                ),
                currentAccountPicture: const CircleAvatar(
                  child: Icon(Icons.person, size: 48),
                ),
              ),

              // Dynamic Payment Method Section
              Consumer<PaymentProvider>(
                builder: (context, provider, child) {
                  return Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withOpacity(0.3)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.payment, color: Colors.blue, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Payment Preference',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.blue[800],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          provider.selectedPaymentMethodName,
                          style: const TextStyle(fontSize: 14),
                        ),
                        if (provider.selectedPaymentMethod?.type.name ==
                            'wallet') ...[
                          const SizedBox(height: 4),
                          Text(
                            'Balance: ${provider.getFormattedWalletBalance()}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ],
                    ),
                  );
                },
              ),

              // Vehicle Information Section
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.directions_car,
                          color: Colors.green,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Vehicle Info',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.green[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${driver.vehicleType} • ${driver.vehicleNumber}',
                      style: const TextStyle(fontSize: 14),
                    ),
                    Text(
                      '${driver.totalRides} rides completed',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              ListTile(
                leading: const Icon(Icons.person),
                title: const Text('Profile'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to profile
                },
              ),
              ListTile(
                leading: const Icon(Icons.account_balance_wallet),
                title: const Text('Wallet'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WalletScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.payment),
                title: const Text('Payment Methods'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PaymentMethodsScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.settings),
                title: const Text('Settings'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to settings
                },
              ),
              ListTile(
                leading: const Icon(Icons.help),
                title: const Text('Help & Support'),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to help
                },
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.logout),
                title: const Text('Logout'),
                onTap: () {
                  Navigator.pop(context);
                  // Handle logout
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
