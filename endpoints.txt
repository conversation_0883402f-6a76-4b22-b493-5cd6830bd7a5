SEPESHA API ENDPOINTS DOCUMENTATION
=====================================

Based on the fully implemented features in the backend, here are the available API endpoints with their request/response formats:

BASE URL: https://your-domain.com/api/

AUTHENTICATION
==============
All protected endpoints require JW<PERSON> token in Authorization header:
Authorization: Bearer your_jwt_token_here

1. USER MANAGEMENT & AUTHENTICATION
===================================

1.1 Register User
POST /register
Content-Type: application/json

Request:
{
  "first_name": "<PERSON>",
  "middle_name": "<PERSON><PERSON>", // optional
  "last_name": "Smith",
  "region_id": 1,
  "phonecode": "255",
  "email": "<EMAIL>",
  "referal_code": "REF123", // optional
  "user_type": "driver|vendor|agent|customer",
  "business_description": "Business desc", // required if vendor
  "profile_photo": "file", // required if driver
  "attachment": "file", // required if driver
  "password": "password123",
  "licence_number": "LIC123", // required if driver
  "licence_expiry": "2025-12-31" // required if driver
}

Response:
{
  "status": true,
  "message": "User registered successfully",
  "data": {
    "user_id": "uuid",
    "otp": "123456",
    "otp_expires_at": "2024-01-01T12:00:00Z"
  }
}

1.2 Login
POST /login
Content-Type: application/json

Request:
{
  "phone": "************",
  "user_type": "driver|vendor|agent|customer"
}

Response:
{
  "status": true,
  "message": "OTP sent successfully",
  "data": {
    "otp": "123456",
    "otp_expires_at": "2024-01-01T12:00:00Z"
  }
}

1.3 Verify OTP
POST /verify-otp
Content-Type: application/json

Request:
{
  "phone": "************",
  "user_type": "driver",
  "otp": "123456"
}

Response:
{
  "status": true,
  "message": "Login successful",
  "data": {
    "access_token": "jwt_token_here",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "auth_key": "uuid",
      "name": "John",
      "sname": "Smith",
      "email": "<EMAIL>",
      "phone": "************",
      "role": "driver",
      "is_verified": 1,
      "wallet_balance_tzs": "0.00",
      "wallet_balance_usd": "0.00",
      "preferred_payment_method": "cash"
    }
  }
}

1.4 Update Profile
POST /user/update-profile/{id}
Authorization: Bearer token
Content-Type: multipart/form-data

Request:
{
  "name": "John",
  "sname": "Smith",
  "email": "<EMAIL>",
  "preferred_payment_method": "cash|wallet|card|bank",
  "profile_photo": "file" // optional
}

Response:
{
  "status": true,
  "message": "Profile updated successfully",
  "data": { user_object }
}

2. VEHICLE MANAGEMENT
=====================

2.1 Create Vehicle
POST /vehicle
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "plate_number": "T123ABC",
  "make": "Toyota",
  "model": "Corolla",
  "year": "2020",
  "color": "White",
  "weight": 1500.5,
  "capacity": 4,
  "longitude": "-122.4194",
  "latitude": "37.7749",
  "created_by": 1,
  "driver_id": "driver_uuid",
  "fee_category_id": "category_uuid",
  "owner_id": "owner_uuid",
  "attachments": [
    {"id": 1},
    {"id": 2}
  ]
}

Response:
{
  "status": true,
  "message": "Vehicle created successfully",
  "data": {
    "id": "vehicle_uuid",
    "plate_number": "T123ABC",
    "make": "Toyota",
    "model": "Corolla",
    "year": "2020",
    "status": "active"
  }
}

2.2 Get All Vehicles
GET /vehicles
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Vehicles retrieved successfully",
  "data": [
    {
      "id": "vehicle_uuid",
      "plate_number": "T123ABC",
      "make": "Toyota",
      "model": "Corolla",
      "driver": { driver_info },
      "category": { fee_category_info }
    }
  ]
}

2.3 Get Vehicle by ID
GET /vehicle/{id}
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Vehicle found",
  "data": {
    "id": "vehicle_uuid",
    "plate_number": "T123ABC",
    "make": "Toyota",
    "model": "Corolla",
    "category": { fee_category_info },
    "attachments": [ attachment_files ]
  }
}

3. BOOKING/TRIP MANAGEMENT
==========================

3.1 Calculate Fare
POST /calculate-fare
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "pickup_latitude": 12.34567890,
  "pickup_longitude": -45.67891234,
  "delivery_latitude": 23.45678901,
  "delivery_longitude": -56.78912345
}

Response:
{
  "status": true,
  "message": "Fare calculated successfully",
  "data": {
    "distance_km": 10.5,
    "estimated_duration_minutes": 25,
    "fare_estimates": [
      {
        "vehicle_type": "Motorcycle",
        "base_fare": 2000,
        "distance_fare": 5000,
        "total_fare": 7000,
        "currency": "TZS"
      },
      {
        "vehicle_type": "Car",
        "base_fare": 3000,
        "distance_fare": 8000,
        "total_fare": 11000,
        "currency": "TZS"
      }
    ]
  }
}

3.2 Find Available Drivers
POST /find-drivers
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "pickup_latitude": 12.34567890,
  "pickup_longitude": -45.67891234,
  "vehicle_type": "car",
  "radius_km": 5
}

Response:
{
  "status": true,
  "message": "Drivers found",
  "data": {
    "available_drivers": [
      {
        "driver_id": "driver_uuid",
        "driver_name": "John Doe",
        "vehicle_info": {
          "plate_number": "T123ABC",
          "make": "Toyota",
          "model": "Corolla"
        },
        "distance_km": 2.5,
        "estimated_arrival_minutes": 8,
        "rating": 4.8,
        "total_trips": 150
      }
    ]
  }
}

3.3 Create Ride Booking
POST /create-ride-booking
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "pickup_latitude": 12.34567890,
  "pickup_longitude": -45.67891234,
  "delivery_latitude": 23.45678901,
  "delivery_longitude": -56.78912345,
  "pickup_location": "Start Location",
  "delivery_location": "End Location",
  "distance_km": 10,
  "estimated_fare": 50.00
}

Response:
{
  "status": true,
  "message": "Booking created successfully",
  "data": {
    "booking_id": "booking_uuid",
    "status": "pending",
    "pickup_location": "Start Location",
    "delivery_location": "End Location",
    "estimated_fare": 50.00,
    "created_at": "2024-01-01T12:00:00Z"
  }
}

3.4 Driver Response to Booking
POST /driver-response
Authorization: Bearer token (driver only)
Content-Type: application/json

Request:
{
  "booking_id": "booking_uuid",
  "response": "accept|decline"
}

Response:
{
  "status": true,
  "message": "Response recorded successfully",
  "data": {
    "booking_id": "booking_uuid",
    "driver_response": "accept",
    "status": "assigned"
  }
}

3.5 Get User Bookings
POST /bookings
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "customer_id": "customer_uuid",
  "status": "pending|assigned|intransit|completed|cancelled"
}

Response:
{
  "status": true,
  "message": "Bookings retrieved",
  "data": [
    {
      "id": "booking_uuid",
      "status": "assigned",
      "pickup_location": "Start Location",
      "delivery_location": "End Location",
      "fare": 50.00,
      "driver": { driver_info },
      "category": { vehicle_category }
    }
  ]
}

4. IN-APP MESSAGING SYSTEM
===========================

4.1 Send Message
POST /send-message
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "sender_id": "sender_uuid",
  "recipient_id": "recipient_uuid",
  "booking_id": "booking_uuid", // optional
  "message": "Hello, what's your ETA?",
  "message_type": "text|image|location|audio|video",
  "attachment": "file_url" // optional
}

Response:
{
  "status": true,
  "message": "Message sent successfully",
  "data": {
    "id": "message_uuid",
    "sender_id": "sender_uuid",
    "recipient_id": "recipient_uuid",
    "booking_id": "booking_uuid",
    "message": "Hello, what's your ETA?",
    "message_type": "text",
    "created_at": "2024-01-01T12:00:00Z"
  }
}

4.2 Get Conversation Messages
GET /messages?sender_id=uuid&recipient_id=uuid&booking_id=uuid
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Messages retrieved",
  "data": [
    {
      "id": "message_uuid",
      "sender_id": "sender_uuid",
      "recipient_id": "recipient_uuid",
      "message": "Hello, what's your ETA?",
      "message_type": "text",
      "is_read": false,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}

Message Endpoints:
POST   http://*************:8000/api/send-message
GET    http://*************:8000/api/messages
POST   http://*************:8000/api/messages/mark-read
GET    http://*************:8000/api/messages/unread-count
POST   http://*************:8000/api/messages/upload-attachment

Conversation Endpoints:
GET    http://*************:8000/api/conversations
POST   http://*************:8000/api/conversations
DELETE http://*************:8000/api/conversations/{id}
GET    http://*************:8000/api/conversations/search

WebSocket Endpoint:
WS     ws://*************:8000/ws?user_id={userId}

5. REAL-TIME LOCATION TRACKING
===============================

5.1 Update Location
POST /update-location
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "user_id": "user_uuid",
  "latitude": 37.7749,
  "longitude": -122.4194,
  "accuracy": 10.5,
  "speed": 5.2,
  "heading": 90.0,
  "altitude": 50.0,
  "booking_id": "booking_uuid" // optional
}

Response:
{
  "status": true,
  "message": "Location broadcasted successfully"
}

5.2 Get Driver Location
GET /driver-location?driver_id=uuid
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Location found",
  "data": {
    "driver_id": "driver_uuid",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "accuracy": 10.5,
    "speed": 5.2,
    "heading": 90.0,
    "last_updated": "2024-01-01T12:00:00Z"
  }
}

5.3 Get Location History
GET /user-location-history?user_id=uuid&start_time=2023-06-14T00:00:00Z&end_time=2023-06-15T23:59:59Z&booking_id=uuid&limit=50
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Location history retrieved",
  "data": [
    {
      "id": "location_uuid",
      "user_id": "user_uuid",
      "latitude": 37.7749,
      "longitude": -122.4194,
      "accuracy": 10.5,
      "speed": 5.2,
      "booking_id": "booking_uuid",
      "timestamp": "2024-01-01T12:00:00Z"
    }
  ]
}

5.4 Get Active Drivers
GET /active-drivers?latitude=37.7749&longitude=-122.4194&radius=10
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Active drivers found",
  "data": [
    {
      "driver_id": "driver_uuid",
      "driver_name": "John Doe",
      "latitude": 37.7749,
      "longitude": -122.4194,
      "distance_km": 2.5,
      "vehicle_info": {
        "plate_number": "T123ABC",
        "make": "Toyota"
      }
    }
  ]
}

6. DRIVER REVIEWS & RATING SYSTEM
==================================

6.1 Create Driver Review
POST /driver-rating/create
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "driver_id": "driver_uuid",
  "rating": 5,
  "review": "Excellent service! Very professional driver."
}

Response:
{
  "status": true,
  "message": "Review created successfully",
  "data": {
    "id": "review_uuid",
    "driver_id": "driver_uuid",
    "user_id": "reviewer_uuid",
    "rating": 5,
    "review": "Excellent service! Very professional driver.",
    "created_at": "2024-01-01T12:00:00Z"
  }
}

6.2 Get Driver Reviews
GET /driver-rating/{driver_id}
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Reviews retrieved",
  "data": {
    "driver_id": "driver_uuid",
    "total_reviews": 25,
    "average_rating": 4.8,
    "reviews": [
      {
        "id": "review_uuid",
        "rating": 5,
        "review": "Excellent service!",
        "user": {
          "reviewer_id": "reviewer_uuid",
          "reviewer_name": "Jane Smith",
          "reviewer_photo": "photo_url"
        },
        "created_at": "2024-01-01T12:00:00Z"
      }
    ]
  }
}

7. SUPPORT SYSTEM
=================

7.1 Create Support Ticket
POST /support-ticket/create
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "subject": "Payment Issue",
  "priority": "low|medium|high",
  "category": "Payment|Technical|General",
  "message": "I'm having trouble with payment processing",
  "attachment": "file" // optional (jpg, png, pdf, max 2MB)
}

Response:
{
  "status": true,
  "message": "Support ticket created successfully",
  "data": {
    "id": "ticket_uuid",
    "subject": "Payment Issue",
    "status": "open",
    "priority": "medium",
    "category": "Payment",
    "sender_id": "user_uuid",
    "created_at": "2024-01-01T12:00:00Z"
  }
}

7.2 Get User Support Tickets
GET /support-tickets
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Tickets retrieved",
  "data": [
    {
      "id": "ticket_uuid",
      "subject": "Payment Issue",
      "status": "open|in_progress|resolved|closed",
      "priority": "medium",
      "category": "Payment",
      "created_at": "2024-01-01T12:00:00Z",
      "messages": [
        {
          "id": "message_uuid",
          "message": "I'm having trouble with payment",
          "sender_type": "user|support",
          "created_at": "2024-01-01T12:00:00Z"
        }
      ]
    }
  ]
}

7.3 Get Support Ticket Details
GET /support-ticket/{id}
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Ticket found",
  "data": {
    "id": "ticket_uuid",
    "subject": "Payment Issue",
    "status": "open",
    "priority": "medium",
    "category": "Payment",
    "messages": [ message_array ],
    "attachments": [ attachment_array ]
  }
}

7.4 Get Support Contacts (Public)
GET /support-contacts

Response:
{
  "status": true,
  "message": "Support contacts retrieved",
  "data": [
    {
      "id": 1,
      "name": "Customer Support",
      "phone": "+************",
      "email": "<EMAIL>",
      "department": "General Support",
      "working_hours": "24/7"
    },
    {
      "id": 2,
      "name": "Technical Support",
      "phone": "+************",
      "email": "<EMAIL>",
      "department": "Technical Issues",
      "working_hours": "8AM - 6PM"
    }
  ]
}

8. PAYMENT METHODS & WALLET
============================

8.1 Payment Methods Available:
- cash: Cash payment
- wallet: Sepesha wallet balance
- card: Credit/Debit card
- bank: Bank transfer

8.2 Wallet Balance (included in user profile):
- wallet_balance_tzs: Balance in Tanzanian Shillings
- wallet_balance_usd: Balance in US Dollars
- preferred_payment_method: User's preferred payment option

9. WEBSOCKET REAL-TIME EVENTS
==============================

WebSocket URL: ws://your-domain.com:6001/app/your-app-key
Authentication: Include JWT token in connection headers

9.1 Location Updates Event
Event: location-updated
Channel: user.{user_id} or booking.{booking_id}

Data:
{
  "event": "location-updated",
  "data": {
    "user_id": "user_uuid",
    "user_type": "driver",
    "latitude": 37.7749,
    "longitude": -122.4194,
    "accuracy": 10.5,
    "speed": 5.2,
    "booking_id": "booking_uuid",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}

9.2 Message Sent Event
Event: message-sent
Channel: user.{recipient_id}

Data:
{
  "event": "message-sent",
  "data": {
    "id": "message_uuid",
    "sender_id": "sender_uuid",
    "recipient_id": "recipient_uuid",
    "message": "Hello!",
    "message_type": "text",
    "booking_id": "booking_uuid",
    "created_at": "2024-01-01T12:00:00Z"
  }
}

9.3 Booking Status Update Event
Event: booking-status-updated
Channel: user.{customer_id} and user.{driver_id}

Data:
{
  "event": "booking-status-updated",
  "data": {
    "booking_id": "booking_uuid",
    "status": "assigned|intransit|completed|cancelled",
    "driver_id": "driver_uuid",
    "customer_id": "customer_uuid",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}

10. ADDITIONAL ENDPOINTS
========================

10.1 Get Regions
GET /regions
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Regions retrieved",
  "data": [
    {
      "id": 1,
      "name": "Dar es Salaam",
      "code": "DSM",
      "country": "Tanzania"
    }
  ]
}

10.2 Get Fee Categories (Vehicle Types)
GET /categories
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Categories retrieved",
  "data": [
    {
      "id": "category_uuid",
      "name": "Motorcycle",
      "base_fare": 2000,
      "per_km_rate": 500,
      "description": "Motorcycle delivery service"
    },
    {
      "id": "category_uuid",
      "name": "Car",
      "base_fare": 3000,
      "per_km_rate": 800,
      "description": "Car ride service"
    }
  ]
}

NOTES:
======
- All monetary values are in Tanzanian Shillings (TZS) unless specified
- UUIDs are used for most entity identifiers
- JWT tokens expire after 1 hour by default
- WebSocket connections require proper authentication
- File uploads support jpg, png, pdf formats with 2MB max size
- Real-time events are broadcasted via WebSocket for live updates
- All timestamps are in ISO 8601 format (UTC)

