[+1823 ms] I/OpenGLRenderer(16383): Davey! duration=247021ms; Flags=1, FrameTimelineVsyncId=9453406, IntendedVsync=142605770640739, Vsync=142605770640739, InputEventId=366549050,
HandleInputStart=142605771646905, AnimationStart=142605771649145, PerformTraversalsStart=142605771650186, DrawStart=142605801853835, FrameDeadline=142605803974071,
FrameInterval=142605771641853, FrameStartTime=16666666, SyncQueued=142605802060033, SyncStart=142605802188627, IssueDrawCommandsStart=142605802265033, SwapBuffers=142605810019982, 
FrameCompleted=142852792700680, DequeueBufferDuration=7047293, QueueBufferDuration=1589479, GpuCompleted=142852792700680, SwapBuffersCompleted=142605811833420,
DisplayPresentTime=0, 
[+8116 ms] I/flutter (16383): User: Instance of 'UserData'
[ +276 ms] I/flutter (16383): === REGISTER DRIVER REQUEST ===
[   +2 ms] I/flutter (16383): URL: https://api.sepesha.com/api/register
[   +2 ms] I/flutter (16383): Fields: {first_name: Driver, middle_name: Michael, last_name: Doe, email: <EMAIL>, phone: 712345699, phonecode: 255, region_id: 1,
user_type: driver, password: password123, password_confirmation: password123, privacy_checked: 1, referal_code: , licence_number: 123456, licence_expiry: 2026-07-31}
[   +2 ms] I/flutter (16383): Files: (profile_photo: image_cropper_1752834043845.jpg, attachment: documents_1752834393626.zip)
[   +1 ms] I/flutter (16383): ============================
[+2769 ms] I/flutter (16383): === REGISTER DRIVER RESPONSE ===
[   +2 ms] I/flutter (16383): Status Code: 201
[   +2 ms] I/flutter (16383): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date:  
Fri, 18 Jul 2025 10:26:38 GMT, status: 201 Created, content-length: 366, content-type: application/json, via: 0.0 Caddy}
[  +10 ms] I/flutter (16383): Response Body: {"status":true,"message":"Registration
successful.","data":{"first_name":"Driver","middle_name":"Michael","last_name":"Doe","phonecode":"255","phone_number":"712345699","email":"<EMAIL>","profile_photo":{},"a
ttachment":{},"user_type":"driver","is_verified":0,"uid":"c06019c4-ac03-4761-b1c5-9f236a98c589","otp":7058,"otp_expires_at":"2025-07-18 10:30:37"}}
[+5487 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F....ID 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[  +31 ms] I/AssistStructure(16383): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +92 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[ +176 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[+1089 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[  +15 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[  +11 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[   +2 ms] I/AssistStructure(16383): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +10 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring off
[   +5 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[  +13 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[   +1 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[ +147 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[   +6 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[   +2 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[   +2 ms] I/AssistStructure(16383): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +66 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring off
[   +9 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[   +7 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[  +15 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[   +1 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[ +581 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[   +5 ms] D/InputMethodManager(16383): showSoftInput() view=io.flutter.embedding.android.FlutterView{1a7e179 VFE...... .F...... 0,0-1080,2088 #1 aid=1073741824} flags=0
reason=SHOW_SOFT_INPUT
[  +70 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[   +2 ms] I/AssistStructure(16383): Flattened final assist data: 400 bytes, containing 1 windows, 3 views
[  +34 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring off
[   +4 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring on
[  +14 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[   +6 ms] D/InsetsController(16383): show(ime(), fromIme=true)
[  +49 ms] I/flutter (16383): === VERIFY OTP REQUEST ===
[   +1 ms] I/flutter (16383): URL: https://api.sepesha.com/api/verify-otp
[   +1 ms] I/flutter (16383): Headers: {Content-Type: application/json}
[   +1 ms] I/flutter (16383): Body: {"phone":712345699,"otp":7058,"user_type":"customer"}
[        ] I/flutter (16383): ==================
[+1166 ms] I/flutter (16383): === VERIFY OTP RESPONSE ===
[   +2 ms] I/flutter (16383): Status Code: 404
[   +2 ms] I/flutter (16383): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date:  
Fri, 18 Jul 2025 10:26:47 GMT, status: 404 Not Found, content-length: 71, content-type: application/json, via: 0.0 Caddy}
[   +2 ms] I/flutter (16383): Response Body: {"status":false,"message":"Invalid credentials","code":404,"data":null}
[   +2 ms] I/flutter (16383): ===================
[ +459 ms] D/MapsInitializer(16383): preferredRenderer: null
[   +2 ms] D/zzcc    (16383): preferredRenderer: null
[   +2 ms] I/zzcc    (16383): Making Creator dynamically
[  +24 ms] W/mpany.sepeshap(16383): ClassLoaderContext classpath size mismatch. expected=1, found=0
(DLC[];PCL[base.apk**********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/sys
tem/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar*3
*********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]} | DLC[];PCL[])
[   +8 ms] I/DynamiteModule(16383): Considering local module com.google.android.gms.maps_core_dynamite:0 and remote module com.google.android.gms.maps_core_dynamite:252130101
[   +2 ms] I/DynamiteModule(16383): Selected remote version of com.google.android.gms.maps_core_dynamite, version >= 252130101
[   +2 ms] V/DynamiteModule(16383): Dynamite loader version >= 2, using loadModule2NoCrashUtils
[  +20 ms] W/System  (16383): ClassLoader referenced unknown path: 
[   +1 ms] D/nativeloader(16383): Configuring clns-6 for other apk . target_sdk_version=36, uses_libraries=,
library_path=/data/app/~~OaYtqV8Bt16yF2IH4NZKdg==/com.google.android.gms-iv2kJvGkv8dI8FgaXpRaZg==/lib/arm64:/data/app/~~OaYtqV8Bt16yF2IH4NZKdg==/com.google.android.gms-iv2kJvGkv8dI
8FgaXpRaZg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.google.android.gms
[ +677 ms] I/mpany.sepeshap(16383): hiddenapi: Accessing hidden method Ldalvik/system/VMStack;->getStackClass2()Ljava/lang/Class; (runtime_flags=0, domain=core-platform,
api=unsupported) from Lm140/gmg; (domain=app) using reflection: allowed
[  +29 ms] D/nativeloader(16383): Configuring clns-7 for other apk . target_sdk_version=35, uses_libraries=ALL,
library_path=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
[  +16 ms] D/nativeloader(16383): Load /data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk!/lib/arm64-v8a/libgmm-jni.so
using isolated ns clns-7 (caller=/data/user_de/0/com.google.android.gms/app_chimera/m/0000000b/dl-MapsCoreDynamite.integ_252130101100400.apk): ok
[   +1 ms] I/native  (16383): I0000 00:00:1752834406.936596   16383 jni_init.cc:30] Initializing JNI...
[   +4 ms] I/Google Android Maps SDK(16383): Google Play services client version: 18020000
[  +38 ms] D/bo      (16383): SDK type: 1, version: 252130101
[  +17 ms] D/ho      (16383): maps_core_dynamite module version in use (0 represents standalone library): 252130101
[   +2 ms] D/ho      (16383): Added event: 109
[   +1 ms] D/ho      (16383): Added event: 112
[        ] D/MapsInitializer(16383): loadedRenderer: LATEST
[        ] D/zzcc    (16383): preferredRenderer: null
[        ] D/bo      (16383): SDK type: 1, version: 252130101
[        ] I/Google Android Maps SDK(16383): Google Play services package version: 252533029
[        ] I/Google Android Maps SDK(16383): Google Play services maps renderer version(maps_core): 252130101
[        ] D/bo      (16383): SDK type: 1, version: 252130101
[        ] D/de      (16383): about to start loading native library asynchronously
[  +10 ms] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +3 ms] I/o       (16383): Using GMM server: https://clients4.google.com/glm/mmap
[        ] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[        ] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
[   +6 ms] D/o       (16383): Using Non-null serverVersionMetadataManager to load previous metadata.
[  +30 ms] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[   +2 ms] I/bs      (16383): Selected MapView map renderer: P
[        ] D/ho      (16383): maps_core_dynamite module version in use (0 represents standalone library): 252130101
[   +3 ms] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[        ] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskWriteViolation
[   +1 ms] W/x       (16383): Suppressed StrictMode policy violation: StrictModeDiskReadViolation
[  +41 ms] D/CompatibilityChangeReporter(16383): Compat change id reported: 171228096; UID 10259; state: ENABLED
[ +160 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +39 ms] W/Settings(16383): Setting device_provisioned has moved from android.provider.Settings.System to android.provider.Settings.Global, returning read-only value.
[  +18 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +12 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[ +143 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +17 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] I/PlatformViewsController(16383): Hosting view in view hierarchy for platform view: 0
[   +3 ms] I/PlatformViewsController(16383): PlatformView is using SurfaceProducer backend
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +19 ms] W/JavaCronetEngine(16383): using the fallback Cronet Engine implementation. Performance will suffer and many HTTP client features, including caching, will not work.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +56 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +11 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] I/GoogleMapController(16383): Installing custom TextureView driven invalidator.
[   +1 ms] E/GoogleMapController(16383): Cannot enable MyLocation layer as location permissions are not granted
[  +49 ms] D/InputConnectionAdaptor(16383): The input method toggled cursor monitoring off
[  +35 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +1 ms] D/ImageReaderSurfaceProducer(16383): ImageTextureEntry can't wait on the fence on Android < 33
[  +71 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[ +178 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[ +167 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +26 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +64 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +50 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +31 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +11 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +25 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +6 ms] W/ImageReader_JNI(16383): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[   +9 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +7 ms] W/ImageReader_JNI(16383): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +27 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +1 ms] W/DynamiteModule(16383): Local module descriptor class for com.google.android.gms.googlecertificates not found.
[  +10 ms] I/DynamiteModule(16383): Considering local module com.google.android.gms.googlecertificates:0 and remote module com.google.android.gms.googlecertificates:7
[  +19 ms] I/DynamiteModule(16383): Selected remote version of com.google.android.gms.googlecertificates, version >= 7
[        ] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +25 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] W/mpany.sepeshap(16383): ClassLoaderContext classpath element checksum mismatch. expected=*********, found=22067137
(DLC[];PCL[base.apk**********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]#PCL[/sys
tem/framework/com.android.media.remotedisplay.jar***********]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system/framework/org.apache.http.legacy.jar*3
*********]#PCL[/apex/com.android.extservices/javalib/android.ext.adservices.jar**********]} |
DLC[];PCL[/data/app/~~nombVVDYFcxwrgKZ0sxQNw==/com.sepeshacompany.sepeshapp-Rfz3BNAyipHTAOHuYdnLOw==/base.apk*22067137]{PCL[/system/framework/org.apache.http.legacy.jar***********]
})
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +22 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +20 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +4 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +54 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +6 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +35 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +27 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +11 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[+1177 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +62 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[ +161 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +91 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +33 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +11 ms] I/mpany.sepeshap(16383): Background young concurrent copying GC freed 7730KB AllocSpace bytes, 72(8296KB) LOS objects, 50% free, 14MB/28MB, paused 189us,248us total
103.104ms
[   +7 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[ +282 ms] I/mpany.sepeshap(16383): Compiler allocated 6723KB to compile void m140.ehv.W(m140.emf, m140.fej, android.content.res.Resources, m140.eka, m140.eut, boolean, m140.djk,
java.util.Map, boolean, boolean, boolean, boolean)
[ +318 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +2 ms] W/ImageReader_JNI(16383): Unable to acquire a buffer item, very likely client tried to acquire more than maxImages buffers
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[        ] I/mpany.sepeshap(16383): Compiler allocated 5744KB to compile void m140.epo.o()
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] I/mpany.sepeshap(16383): Background concurrent copying GC freed 7147KB AllocSpace bytes, 26(8236KB) LOS objects, 66% free, 13MB/41MB, paused 187us,200us total 104.237ms
[  +21 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +8 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +9 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +37 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +58 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +21 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +25 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +29 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +31 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +45 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +27 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +6 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +8 ms] I/mpany.sepeshap(16383): Compiler allocated 4483KB to compile void m140.ecu.c(m140.djz, m140.ebm, m140.edp, m140.ekm, boolean)
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +32 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +2 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +50 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +49 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +34 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +65 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +4 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +23 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +23 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +59 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +19 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +59 ms] I/mpany.sepeshap(16383): Compiler allocated 4191KB to compile m140.emb m140.emf.l(m140.azx, m140.iai, m140.elt, byte[], boolean, m140.cdn, m140.dex, java.lang.Iterable)
[  +29 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] I/mpany.sepeshap(16383): Background young concurrent copying GC freed 10045KB AllocSpace bytes, 122(9940KB) LOS objects, 32% free, 28MB/41MB, paused 680us,599us total
110.783ms
[        ] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +49 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +67 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +13 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +17 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +51 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[+8639 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] D/Google Android Maps SDK(16383): For capability in capabilities, log:
[   +2 ms] D/Google Android Maps SDK(16383): "AdvancedMarkers: false: Capabilities unavailable without a Map ID."Data-driven styling: false
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[+1538 ms] D/MapsInitializer(16383): preferredRenderer: null
[   +2 ms] D/zzcc    (16383): preferredRenderer: null
[   +2 ms] I/Google Android Maps SDK(16383): Google Play services package version: 252533029
[   +1 ms] I/Google Android Maps SDK(16383): Google Play services maps renderer version(maps_core): 252130101
[  +38 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +25 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +5 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +6 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +20 ms] I/PlatformViewsController(16383): Hosting view in view hierarchy for platform view: 1
[   +1 ms] I/PlatformViewsController(16383): PlatformView is using SurfaceProducer backend
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +3 ms] I/GoogleMapController(16383): Installing custom TextureView driven invalidator.
[  +44 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +24 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] D/ImageReaderSurfaceProducer(16383): ImageTextureEntry can't wait on the fence on Android < 33
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +27 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +23 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +7 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +7 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +21 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +33 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +28 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +2 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[ +161 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[   +8 ms] I/mpany.sepeshap(16383): Background concurrent copying GC freed 13MB AllocSpace bytes, 91(11MB) LOS objects, 63% free, 27MB/75MB, paused 402us,152us total 282.522ms     
[  +10 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +18 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +20 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +12 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +16 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +43 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +16 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +22 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +11 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +3 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +8 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[        ] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[  +10 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[   +9 ms] W/ProxyAndroidLoggerBackend(16383): Too many Flogger logs received before configuration. Dropping old logs.
[+1303 ms] E/flutter (16383): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: PlatformException(channel-error, Unable to establish connection on channel:   
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[   +1 ms] E/flutter (16383): #0      MapsApi.animateCamera (package:google_maps_flutter_android/src/messages.g.dart:2278:7)
[        ] E/flutter (16383): <asynchronous suspension>
[        ] E/flutter (16383): 
[ +113 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +14 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +19 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[ +527 ms] I/flutter (16383): Error moving camera to current location: PlatformException(channel-error, Unable to establish connection on channel:
"dev.flutter.pigeon.google_maps_flutter_android.MapsApi.animateCamera.0"., null, null)
[ +124 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +15 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
[  +23 ms] E/FrameEvents(16383): updateAcquireFence: Did not find frame.
