import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/widgets/app_drawer_header.dart';
import 'package:sepesha_app/widgets/drawer_tile.dart';
import 'package:sepesha_app/screens/payment_methods_screen.dart';
import 'package:sepesha_app/screens/conversation_list_screen.dart';
import 'package:sepesha_app/screens/dashboard/rides_screen.dart';
import 'package:sepesha_app/Driver/history/presentation/history_screen.dart';
import 'package:sepesha_app/provider/customer_history_provider.dart';
import 'package:sepesha_app/services/auth_services.dart';
import 'package:sepesha_app/services/session_manager.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  String? userType;
  int unreadMessages = 0;

  @override
  void initState() {
    super.initState();
    _loadUserType();
    _loadUnreadCount();
  }

  void _loadUserType() {
    final sessionUser = SessionManager.instance.user;
    if (sessionUser != null) {
      userType = sessionUser.userType;
      setState(() {});
    }
  }

  void _loadUnreadCount() {
    // TODO: Load actual unread message count from MessageProvider
    // For now, using placeholder
    unreadMessages = 0;
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // Header
          const AppDrawerHeader(),

          // Navigation Tiles
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Payment Methods
                DrawerTile(
                  icon: Icons.payment,
                  title: 'Payment Methods',
                  subtitle: 'Manage your payment options',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PaymentMethodsScreen(),
                      ),
                    );
                  },
                ),

                // Messages
                DrawerTile(
                  icon: Icons.message,
                  title: 'Messages',
                  subtitle: 'Chat with drivers/customers',
                  showBadge: unreadMessages > 0,
                  badgeText:
                      unreadMessages > 0 ? unreadMessages.toString() : null,
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ConversationsScreen(),
                      ),
                    );
                  },
                ),

                // Ride History
                DrawerTile(
                  icon: Icons.history,
                  title: 'Ride History',
                  subtitle:
                      userType == 'driver'
                          ? 'Your completed rides'
                          : 'Your trip history',
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToHistory();
                  },
                ),

                const Divider(),

                // About
                DrawerTile(
                  icon: Icons.info_outline,
                  title: 'About',
                  subtitle: 'App information',
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog();
                  },
                ),

                // Support
                DrawerTile(
                  icon: Icons.support_agent,
                  title: 'Support',
                  subtitle: 'Get help and support',
                  onTap: () {
                    Navigator.pop(context);
                    _navigateToSupport();
                  },
                ),

                const Divider(),

                // Logout
                DrawerTile(
                  icon: Icons.logout,
                  title: 'Logout',
                  subtitle: 'Sign out of your account',
                  iconColor: Colors.red,
                  onTap: () {
                    Navigator.pop(context);
                    _showLogoutDialog();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToHistory() {
    if (userType == 'driver') {
      // Navigate to driver history screen
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const HistoryScreen()),
      );
    } else {
      // Navigate to customer history screen (rides screen) with provider
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => ChangeNotifierProvider(
                create: (_) => CustomerHistoryProvider(),
                child: RidesScreen(),
              ),
        ),
      );
    }
  }

  void _navigateToSupport() {
    // TODO: Navigate to support screen
    // Navigator.push(context, MaterialPageRoute(builder: (context) => SupportScreen()));
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('About Sepesha'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Sepesha - Your Reliable Ride Partner'),
                SizedBox(height: 8),
                Text('Version: 1.0.0'),
                SizedBox(height: 8),
                Text('© 2024 Sepesha. All rights reserved.'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Logout'),
            content: const Text('Are you sure you want to logout?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  AuthServices.logout(context);
                },
                child: const Text(
                  'Logout',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
